﻿#include "ElementProbeDlg.h"
#include "IDock.h"
#include "GbmpNew.h"
#include "IUiView.h"
#include "ICanvas.h"
#include "ICamera.h"
#include "IFamily.h"
#include "IElement.h"
#include "IInstance.h"
#include "IDocument.h"
#include "IUiManager.h"
#include "IParameter.h"
#include "IMainWindow.h"
#include "ISkyOptions.h"
#include "IUiDocument.h"
#include "IFamilyType.h"
#include "IDockManager.h"
#include "IExternalData.h"
#include "IInstanceType.h"
#include "ICanvasOptions.h"
#include "IShadowOptions.h"
#include "IFamilyManager.h"
#include "IGenericElement.h"
#include "IExternalObject.h"
#include "IUserTransaction.h"
#include "IElementPosition.h"
#include "IDocumentManager.h"
#include "IViewHouseOptions.h"
#include "IElementParameters.h"
#include "UiDocumentViewUtils.h"
#include "IVisualEffectManager.h"
#include "IPreHighlightSetting.h"
#include "IFamilyConfigElement.h"
#include "IGeometryRelationship.h"
#include "IUiDocumentViewManager.h"
#include "IExternalDataComponent.h"
#include "IElementBasicInformation.h"
#include "IFamilyParameterDefinition.h"
#include "IParameterDefinitionLibrary.h"
#include "IElementParametersCustomizer.h"
#include "IElementParameterGroupOverride.h"
#include "IGeometryRelationshipComponent.h"
#include "GcmpBuiltInParameterDefinitions.h"
#include "IElementParameterBindings.h"
#include "IParameterBinding.h"

#include "QColor"
#include "qevent.h"
#include "QVariant"
#include "QTreeWidget.h"
#include "ElementWrapper.h"
#include "ObjectBrowser/ObjectBrowser.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

/// 本文件是界面实现的辅助类，仅包含非常少的GDMP API的演示内容

using namespace Sample;
using namespace gcmp;

const std::wstring ID_DOCK_ELEMENT_PROBE = L"DockIdElementProbe";

#pragma region 初始化
ElementProbeDlg::ElementProbeDlg(QWidget *parent)
    : BaseTreeDlg(parent)
{
    SetCanCheck(false);
    // 消息处理。
    connect(m_pQTreeWidget, SIGNAL(itemSelectionChanged()), this, SLOT(OnTreeItemSelected()));
}

ElementProbeDlg::~ElementProbeDlg(void)
{
    disconnect(m_pQTreeWidget, SIGNAL(itemSelectionChanged()), this, SLOT(OnTreeItemSelected()));
}

#pragma endregion 初始化

#pragma region 和GDMP GUI接口相关的初始化和单例获取
ElementProbeDlg* ElementProbeDlg::GetOrCreate()
{
    if (ElementProbeDlg* pDlg = Get())
    {
        return pDlg;
    }
    // 没有则初始化
    IUiManager *pUiManager = IUiManager::Get();
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pUiManager, L"pUiManager为空",L"GDMPLab",L"2024-12-30");
    IDockManager * pDockManager = pUiManager->GetDockManager();
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pDockManager, L"pDockManager为空",L"GDMPLab",L"2024-12-30");
    OwnerPtr<IDock> opDock = pDockManager->CreateDock(ID_DOCK_ELEMENT_PROBE, GBMP_TR(L"图元探针"));
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(opDock, L"opDock为空",L"GDMPLab",L"2024-12-30");

    OwnerPtr<ElementProbeDlg> opDlg = NEW_AS_OWNER_PTR(ElementProbeDlg);
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(opDlg, L"opDlg为空",L"GDMPLab",L"2024-12-30");
    opDlg->setMinimumWidth(200);
    opDock->SetWidget(TransferOwnership(opDlg));

    pDockManager->AddDock(TransferOwnership(opDock), DockArea::Right, DockOrientation::Vertical);

    return Get();
}

void Sample::ElementProbeDlg::ShowDock()
{
    IDock* pDock = GetDock(ID_DOCK_ELEMENT_PROBE);
    DBG_WARN_AND_RETURN_VOID_UNLESS(pDock, L"pDock为空",L"GDMPLab",L"2024-12-30");
    pDock->Show();
}

void Sample::ElementProbeDlg::HideDock()
{
    IDock* pDock = GetDock(ID_DOCK_ELEMENT_PROBE);
    DBG_WARN_AND_RETURN_VOID_UNLESS(pDock, L"pDock为空",L"GDMPLab",L"2024-12-30");
    pDock->Hide();
}

void Sample::ElementProbeDlg::Update()
{
    OnTreeItemSelected();
}

QSize Sample::ElementProbeDlg::sizeHint() const
{
    return QSize(400, 1000);
}

ElementProbeDlg * ElementProbeDlg::Get()
{
    IDock* pDock = GetDock(ID_DOCK_ELEMENT_PROBE);
    if (!pDock)
    {
        return nullptr;
    }

    ElementProbeDlg* dlg = dynamic_cast<ElementProbeDlg*>(pDock->GetWidget());
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(dlg, L"dlg为空",L"GDMPLab",L"2024-12-30");
    return dlg;
}
#pragma endregion 和GDMP GUI接口相关的初始化和单例获取

#pragma region 视图设置相关的逻辑
namespace
{
    enum ElementParts
    {
        // 基本信息
        IElement,
        //ImplementationUid, ElementId, HostElementId, GroupId, IsInGroupType,
        StaticClassSchema, IElementBasicInformation, IElementStatus, ILayerComponent,
        // 扩展数据和参数
        IElementTags, IElementAttributes, IExternalDataComponent, IElementParameters,IExternalObject,
        // 通用编辑
        IElementDeletionComponent, IElementTransformationComponent, IElementCopyPasteComponent, IElementCopyStrategyComponent, IGroupComponent,
        // 定位
        IElementPosition, IElementPositionPoints, IElementPositionReportComponent,
        // 图形表达和样式
        IElementModelShape, IElementViewSpecificShapeComponent, IGraphicsNodeStyleAndMaterialOverride,
        // 建模和几何关系
        IElementModelingOperations, IGeometryRelationshipComponent, IElementConstraintBehavior, 
        // 交互
        ICommandBehavior,
        // 关联更新
        IElementRegenerationComponent,
        // 子节点
        IParameter, IExternalData, IExternalData_IParameter,
        IPositionGeometry, IPositionAssociatedPlane, 
        IElementParameters_IElementParametersCustomizer, IElementParameters_IElementParametersCustomizer_IParameter,
        IElementParameters_IElementParametersCustomizers, IElementParameters_IElementParametersCustomizers_IParameter,
        IElementParameters_IElementParameterBindings,
        EmbeddedParametersMap, EmbeddedParametersMap_IParameter, 
        IElementParameterGroupOverride, 
        IGeometryRelationshipComponent_IOpeningRelationshipBehavior, 
        IGeometryRelationshipComponent_IOpenedGraphicsElementShapeComponent,IGeometryRelationshipComponent_IOpeningGraphicsElementShapeComponent,
        IGeometryRelationshipComponent_ICutRelationshipBehavior,
        IGeometryRelationshipComponent_ICutterGraphicsElementShapeComponent, IGeometryRelationshipComponent_ICutteeGraphicsElementShapeComponent,
        IGeometryRelationshipComponent_IJoinRelationshipBehavior, IGeometryRelationshipComponent_IGeometryRelationship,
        IInstance, IInstance_IInstanceType, IInstance_IFamilyManager, IInstance_IFamily, IInstance_IFamilyConfigElement,
        IInstance_IElementParameters, IInstance_IFamilyParameterDefinition,
        IInstance_IElementParameters_IParameter, IInstance_IFamilyParameterDefinition_IParameter,
        IInstance_IFamily_IFamilyType, IInstance_IFamily_InstanceType, IInstance_IFamilyManager_IFamilyType,
    };

    QTreeWidgetItem* CreateTreeItem(QTreeWidgetItem* pItem, int widgetData, std::map<int, QTreeWidgetItem*>& itemMap)
    {
        pItem->setData(1, 0, QVariant::fromValue(widgetData));
        itemMap[widgetData] = pItem;
        return pItem;
    }

    QTreeWidgetItem* CreateTreeItem(QTreeWidgetItem* pParent, std::wstring name, int widgetData, std::map<int, QTreeWidgetItem*>& itemMap, bool notNull)
    {
        QTreeWidgetItem* pItem_ModelView = NEW_AS_QT(QTreeWidgetItem, pParent);
        pItem_ModelView->setText(0, QString::fromStdWString(name));
        if (!notNull)
        {
            pItem_ModelView->setTextColor(0, QColor(255, 0, 0));
        }
        return CreateTreeItem(pItem_ModelView, widgetData, itemMap);
    }

    QTreeWidgetItem* CreateTreeItem(QTreeWidget* pParent, std::wstring name, int widgetData, std::map<int, QTreeWidgetItem*>& itemMap)
    {
        QTreeWidgetItem* pItem_ModelView = NEW_AS_QT(QTreeWidgetItem, pParent);
        pItem_ModelView->setText(0, QString::fromStdWString(name));
        return CreateTreeItem(pItem_ModelView, widgetData, itemMap);
    }
}
// 添加树形结点
void Sample::ElementProbeDlg::AddNodes()
{
    m_pQTreeWidget->clear();
    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    if (nullptr == pDoc) { return; }
    const gcmp::IElement* pElem = pDoc->GetElement(m_elementId);
    if (nullptr == pElem) { return; }
    QTreeWidgetItem* pQTreeItemRoot = CreateTreeItem(m_pQTreeWidget, StringUtil::ToWString(typeid(*pElem).name()), ElementParts::IElement, m_itemMap);
    CreateTreeItem(pQTreeItemRoot, L"IElementBasicInformation", ElementParts::IElementBasicInformation, m_itemMap, pElem->GetBasicInformation() != nullptr);
    CreateTreeItem(pQTreeItemRoot, L"IElementStatus", ElementParts::IElementStatus, m_itemMap, pElem->GetStatus() != nullptr);
    CreateTreeItem(pQTreeItemRoot, L"ILayerComponent", ElementParts::ILayerComponent, m_itemMap, pElem->GetLayerComponent() != nullptr);
    CreateTreeItem(pQTreeItemRoot, L"IElementTags", ElementParts::IElementTags, m_itemMap, pElem->GetTags() != nullptr);
    CreateTreeItem(pQTreeItemRoot, L"IElementAttributes", ElementParts::IElementAttributes, m_itemMap, pElem->GetAttributes() != nullptr);
    const gcmp::IElementParameters* pElemParameters = pElem->GetElementParameters();
    QTreeWidgetItem* pQTreeItemParams = CreateTreeItem(pQTreeItemRoot, L"IElementParameters", ElementParts::IElementParameters, m_itemMap, pElemParameters != nullptr);
    if (pElemParameters)
    {
        const std::vector<OwnerPtr<gcmp::IParameter>> opParams = pElemParameters->GetAllParameters();
        for (const auto& opParam : opParams)
        {
            QTreeWidgetItem* pQTreeItemParam = CreateTreeItem(pQTreeItemParams, L"IParameter_" + opParam->GetName(), ElementParts::IParameter, m_itemMap, true);
            pQTreeItemParam->setData(2, 0, QVariant::fromValue(opParam->GetParameterDefinitionId()));
        }
        {
            const std::map<int, OwnerPtr<gcmp::IParameter>>& embeddedParamsMap = pElemParameters->GetEmbeddedParametersMap();
            if (embeddedParamsMap.size() > 0)
            {
                QTreeWidgetItem* pQTreeItemEmbeddedParams = CreateTreeItem(pQTreeItemParams, L"GetEmbeddedParametersMap", ElementParts::EmbeddedParametersMap, m_itemMap, true);
                for (const auto& it : embeddedParamsMap)
                {
                    const OwnerPtr<gcmp::IParameter> opParam = pElemParameters->GetParameterByUid(it.second->GetParameterDefinitionUid());
                    QTreeWidgetItem* pQTreeItemParam = CreateTreeItem(pQTreeItemEmbeddedParams, L"IParameter_" + opParam->GetName(), ElementParts::IParameter, m_itemMap, true);
                    pQTreeItemParam->setData(2, 0, QVariant::fromValue(it.first));
                }
            }
        }
        {
            const gcmp::IElementParameterBindings* pElementParameterBindings = pElemParameters->GetElementParameterBindings();
            QTreeWidgetItem* pQTreeItemParamCustomizer = CreateTreeItem(pQTreeItemParams, L"IElementParameterBindings", ElementParts::IElementParameters_IElementParameterBindings, m_itemMap, pElementParameterBindings != nullptr);
        }
        {
            const gcmp::IElementParametersCustomizer* pElementParametersCustomizer = pElemParameters->GetParametersCustomizer();
            QTreeWidgetItem* pQTreeItemParamCustomizer = CreateTreeItem(pQTreeItemParams, L"IElementParametersCustomizer", ElementParts::IElementParameters_IElementParametersCustomizer, m_itemMap, pElementParametersCustomizer != nullptr);
            std::vector<int> paramIds;
            pElementParametersCustomizer->ReportParameterDefinitions(&paramIds);
            for (auto paramId : paramIds)
            {
                const OwnerPtr<gcmp::IParameter> opParam = pElementParametersCustomizer->GetNativeParameter(paramId);
                if (opParam)
                {
                    QTreeWidgetItem* pQTreeItemParam = CreateTreeItem(pQTreeItemParamCustomizer, L"IParameter_" + opParam->GetName(), ElementParts::IElementParameters_IElementParametersCustomizer_IParameter, m_itemMap, true);
                    pQTreeItemParam->setData(2, 0, QVariant::fromValue(paramId));
                }
            }
        }
        std::map<Guid, const gcmp::IElementParametersCustomizer*> paramCustomizerMap;
        if (pElemParameters->GetAllParametersCustomizers(paramCustomizerMap))
        {
            for (auto& it : paramCustomizerMap)
            {
                QTreeWidgetItem* pQTreeItemParamCustomizer = CreateTreeItem(pQTreeItemParams, L"IElementParametersCustomizer", ElementParts::IElementParameters_IElementParametersCustomizers, m_itemMap, true);
                pQTreeItemParamCustomizer->setData(2, 0, QVariant::fromValue(QString::fromStdWString(GuidUtils::ToString(it.first))));
                std::vector<int> paramIds;
                it.second->ReportParameterDefinitions(&paramIds);
                for (auto paramId : paramIds)
                {
                    const OwnerPtr<gcmp::IParameter> opParam = it.second->GetNativeParameter(paramId);
                    if (opParam)
                    {
                        QTreeWidgetItem* pQTreeItemParam = CreateTreeItem(pQTreeItemParamCustomizer, L"IParameter_" + opParam->GetName(), ElementParts::IElementParameters_IElementParametersCustomizers_IParameter, m_itemMap, true);
                        pQTreeItemParam->setData(2, 0, QVariant::fromValue(QString::fromStdWString(GuidUtils::ToString(it.first))));
                        pQTreeItemParam->setData(3, 0, QVariant::fromValue(paramId));
                    }
                }
            }
        }
        const gcmp::IElementParameterGroupOverride* pParamGroupOverride = pElemParameters->GetElementParameterGroupOverride();
        QTreeWidgetItem* pQTreeItemParamGroupOverride = CreateTreeItem(pQTreeItemParams, L"GetElementParameterGroupOverride", ElementParts::IElementParameterGroupOverride, m_itemMap, pParamGroupOverride != nullptr);
    }
    CreateTreeItem(pQTreeItemRoot, L"IElementRegenerationComponent", ElementParts::IElementRegenerationComponent, m_itemMap, pElem->GetElementRegenerationComponent() != nullptr);
    const gcmp::IExternalDataComponent* pDataComponent = pElem->GetExternalDataComponent();
    QTreeWidgetItem* pQTreeItemDatas = CreateTreeItem(pQTreeItemRoot, L"IExternalDataComponent", ElementParts::IExternalDataComponent, m_itemMap, nullptr != pDataComponent);
    if (pDataComponent)
    {
        const std::vector<OwnerPtr<gcmp::IExternalData>>& exDatas = pDataComponent->GetAllExternalDatas();
        int dataCount = (int)exDatas.size();
        for (int i = 0; i < dataCount; i++)
        {
            QTreeWidgetItem* pQTreeItemData = CreateTreeItem(pQTreeItemDatas, L"IExternalData", ElementParts::IExternalData, m_itemMap, true);
            pQTreeItemData->setData(2, 0, QVariant::fromValue(i));
            const OwnerPtr<gcmp::IExternalData>& opExData = exDatas[i];
            std::vector<int> paramIds;
            opExData->ReportParameterDefinitions(&paramIds);
            for (int j = 0; j < paramIds.size(); j++)
            {
                const OwnerPtr<gcmp::IParameter> opParam = opExData->GetNativeParameter(paramIds[j]);
                QTreeWidgetItem* pQTreeItemParam = CreateTreeItem(pQTreeItemData, L"IParameter_" + opParam->GetName(), ElementParts::IExternalData_IParameter, m_itemMap, true);
                pQTreeItemParam->setData(2, 0, QVariant::fromValue(i));
                pQTreeItemParam->setData(3, 0, QVariant::fromValue(paramIds[j]));
            }
        }
    }
    const IGenericElement* pGenericElem = quick_cast<IGenericElement>(pElem);
    if (pGenericElem)
    {
        const gcmp::IExternalObject* pExtObject = pGenericElem->GetExternalObject();
        QTreeWidgetItem* pQTreeItemExternalObj = CreateTreeItem(pQTreeItemRoot, L"IExternalObject", ElementParts::IExternalObject, m_itemMap, nullptr != pExtObject);
        if (pExtObject)
        {
            CreateTreeItem(pQTreeItemRoot, L"StaticClassSchema", ElementParts::StaticClassSchema, m_itemMap, true);
        }
    }
    CreateTreeItem(pQTreeItemRoot, L"IElementDeletionComponent", ElementParts::IElementDeletionComponent, m_itemMap, pElem->GetElementDeletionComponent() != nullptr);
    CreateTreeItem(pQTreeItemRoot, L"IElementTransformationComponent", ElementParts::IElementTransformationComponent, m_itemMap, pElem->GetElementTransformationComponent() != nullptr);
    CreateTreeItem(pQTreeItemRoot, L"IElementCopyPasteComponent", ElementParts::IElementCopyPasteComponent, m_itemMap, pElem->GetElementTransformationComponent() != nullptr);
    CreateTreeItem(pQTreeItemRoot, L"IElementCopyStrategyComponent", ElementParts::IElementCopyStrategyComponent, m_itemMap, pElem->GetElementCopyStrategyComponent() != nullptr);
    CreateTreeItem(pQTreeItemRoot, L"IGroupComponent", ElementParts::IGroupComponent, m_itemMap, pElem->GetGroupComponent() != nullptr);
    const gcmp::IElementPosition* pElemPosition = pElem->GetElementPosition();
    QTreeWidgetItem* pQTreeItemPosition = CreateTreeItem(pQTreeItemRoot, L"IElementPosition", ElementParts::IElementPosition, m_itemMap, pElemPosition != nullptr);
    CreateTreeItem(pQTreeItemPosition, L"IPositionGeometry", ElementParts::IPositionGeometry, m_itemMap, pElemPosition != nullptr && pElemPosition->GetPositionGeometry() != nullptr);
    CreateTreeItem(pQTreeItemPosition, L"IPositionAssociatedPlane", ElementParts::IPositionAssociatedPlane, m_itemMap, pElemPosition != nullptr && pElemPosition->GetPositionAssociatedPlane() != nullptr);
    CreateTreeItem(pQTreeItemRoot, L"IElementPositionPoints", ElementParts::IElementPositionPoints, m_itemMap, pElem->GetPositionPoints() != nullptr); 
    CreateTreeItem(pQTreeItemRoot, L"IElementPositionReportComponent", ElementParts::IElementPositionReportComponent, m_itemMap, pElem->GetElementPositionReportComponent() != nullptr);
    CreateTreeItem(pQTreeItemRoot, L"IElementModelShape", ElementParts::IElementModelShape, m_itemMap, pElem->GetElementModelShape() != nullptr);
    CreateTreeItem(pQTreeItemRoot, L"IGraphicsNodeStyleAndMaterialOverride", ElementParts::IGraphicsNodeStyleAndMaterialOverride, m_itemMap, pElem->GetShapeGraphicsNodeStyleAndMaterialOverrideComponent() != nullptr);
    CreateTreeItem(pQTreeItemRoot, L"IElementModelingOperations", ElementParts::IElementModelingOperations, m_itemMap, pElem->GetModelingOperations() != nullptr);
    const gcmp::IGeometryRelationshipComponent* pGeomRelationship = pElem->GetGeometryRelationshipComponent();
    QTreeWidgetItem* pQTreeItemGeomRelationship = CreateTreeItem(pQTreeItemRoot, L"IGeometryRelationshipComponent", ElementParts::IGeometryRelationshipComponent, m_itemMap, pGeomRelationship != nullptr);
    if(pGeomRelationship)
    {
        CreateTreeItem(pQTreeItemGeomRelationship, L"GetOpeningRelationship", ElementParts::IGeometryRelationshipComponent_IOpeningRelationshipBehavior, 
            m_itemMap, pGeomRelationship->GetOpeningRelationship() != nullptr);
        CreateTreeItem(pQTreeItemGeomRelationship, L"GetOpenedGraphicsElementShapeComponent", ElementParts::IGeometryRelationshipComponent_IOpenedGraphicsElementShapeComponent,
            m_itemMap, pGeomRelationship->GetOpenedGraphicsElementShapeComponent() != nullptr);
        CreateTreeItem(pQTreeItemGeomRelationship, L"GetOpeningGraphicsElementShapeComponent", ElementParts::IGeometryRelationshipComponent_IOpeningGraphicsElementShapeComponent,
            m_itemMap, pGeomRelationship->GetOpeningGraphicsElementShapeComponent() != nullptr);
        CreateTreeItem(pQTreeItemGeomRelationship, L"GetCutRelationship", ElementParts::IGeometryRelationshipComponent_ICutRelationshipBehavior,
            m_itemMap, pGeomRelationship->GetCutRelationship() != nullptr);
        CreateTreeItem(pQTreeItemGeomRelationship, L"GetCutterGraphicsElementShapeComponent", ElementParts::IGeometryRelationshipComponent_ICutterGraphicsElementShapeComponent,
            m_itemMap, pGeomRelationship->GetCutterGraphicsElementShapeComponent() != nullptr);
        CreateTreeItem(pQTreeItemGeomRelationship, L"GetCutteeGraphicsElementShapeComponent", ElementParts::IGeometryRelationshipComponent_ICutteeGraphicsElementShapeComponent,
            m_itemMap, pGeomRelationship->GetCutteeGraphicsElementShapeComponent() != nullptr);
        CreateTreeItem(pQTreeItemGeomRelationship, L"GetJoinRelationship", ElementParts::IGeometryRelationshipComponent_IJoinRelationshipBehavior,
            m_itemMap, pGeomRelationship->GetJoinRelationship() != nullptr);
        const std::vector<OwnerPtr<IGeometryRelationship>>& opGeomRelationships = pGeomRelationship->GetGeometryRelationships();
        for (int i = 0; i < opGeomRelationships.size(); i++)
        {
            const OwnerPtr<IGeometryRelationship>& opGeomRelationship = opGeomRelationships[i];
            QTreeWidgetItem* pQTreeItemGeomRelationshipItem = CreateTreeItem(pQTreeItemGeomRelationship, L"GetGeometryRelationships", ElementParts::IGeometryRelationshipComponent_IGeometryRelationship,
                m_itemMap, opGeomRelationship != nullptr);
            pQTreeItemGeomRelationshipItem->setData(2, 0, QVariant::fromValue(i));
        }
    }
    CreateTreeItem(pQTreeItemRoot, L"IElementConstraintBehavior", ElementParts::IElementConstraintBehavior, m_itemMap, pElem->GetElementConstraintBehavior() != nullptr);
    CreateTreeItem(pQTreeItemRoot, L"ICommandBehavior", ElementParts::ICommandBehavior, m_itemMap, pElem->GetCommandBehavior() != nullptr);

    const gcmp::IInstance* pInstance = quick_cast<gcmp::IInstance>(pElem);
    if (pInstance)
    {
        QTreeWidgetItem* pQTreeItemInst = CreateTreeItem(pQTreeItemRoot, L"IInstance", ElementParts::IInstance, m_itemMap, true);
        CreateTreeItem(pQTreeItemInst, L"IInstanceType", ElementParts::IInstance_IInstanceType, m_itemMap, true);
        const gcmp::IFamily* pFamily = pInstance->GetFamily();
        {
            QTreeWidgetItem* pQTreeItemFamily = CreateTreeItem(pQTreeItemInst, L"IFamily", ElementParts::IInstance_IFamily, m_itemMap, true);
            std::vector<const IFamilyType*> pFamilyTypesOfFamily = pFamily->GetFamilyTypes();
            for (const IFamilyType* pFamilyType : pFamilyTypesOfFamily)
            {
                QTreeWidgetItem* pQTreeItemFamilyType =
                    CreateTreeItem(pQTreeItemFamily, L"GetFamilyType_" + pFamilyType->GetName(), ElementParts::IInstance_IFamily_IFamilyType, m_itemMap, pFamilyType != nullptr);
                pQTreeItemFamilyType->setData(2, 0, QVariant::fromValue(QString::fromStdWString(pFamilyType->GetName())));
            }
            std::vector<IInstanceType*> pInstanceTypesOfFamily = pFamily->GetInstanceTypes();
            for (const IInstanceType* pInstType : pInstanceTypesOfFamily)
            {
                const gcmp::IElementParameters* pParamBehavior = pInstType->GetElementParameters();
                // 更新"名称"参数
                const OwnerPtr<gcmp::IParameter> nameParameter = pParamBehavior->GetParameterByUid(PARAMETER_UID(ElementNameBuiltInParameter));
                QTreeWidgetItem* pQTreeItemInstType =
                    CreateTreeItem(pQTreeItemFamily, L"GetInstanceType_" + nameParameter->GetValueAsString(), ElementParts::IInstance_IFamily_InstanceType, m_itemMap, pInstType != nullptr);
                pQTreeItemInstType->setData(2, 0, QVariant::fromValue(QString::fromStdWString(nameParameter->GetValueAsString())));
            }
        }
        const IFamilyConfigElement* pIFamilyConfigElement = IFamilyConfigElement::GetFamilyConfigElementByName(pDoc, pFamily->GetBasicInformation()->GetName());
        CreateTreeItem(pQTreeItemInst, L"IFamilyConfigElement", ElementParts::IInstance_IFamilyConfigElement, m_itemMap, true);
        {
            std::wstring familyFilePath = pIFamilyConfigElement->GetFamilyFilePath(pDoc);
            IDocumentManager* pDocManager = IDocumentManager::Get();
            DocumentLoadOptions docLoadOptions(false, false, true, false);
            DocumentLoadResult docLoadResult;
            std::set<std::wstring> unrecognizedAssemblies;
            IDocument* pFamDoc = pDocManager->OpenDocument(familyFilePath, docLoadOptions, docLoadResult, nullptr, &unrecognizedAssemblies);
            const gfam::IFamilyManager* pFamMgr = gfam::IFamilyManager::Get(pFamDoc);
            QTreeWidgetItem* pQTreeItemFamilyMgr = CreateTreeItem(pQTreeItemInst, L"IFamilyManager", ElementParts::IInstance_IFamilyManager, m_itemMap, true);

            std::vector<const IFamilyType*> pFamilyTypesOfFamily = pFamMgr->GetFamilyTypes();
            for (const IFamilyType* pFamilyType : pFamilyTypesOfFamily)
            {
                QTreeWidgetItem* pQTreeItemFamilyType =
                    CreateTreeItem(pQTreeItemFamilyMgr, L"GetFamilyType_" + pFamilyType->GetName(), ElementParts::IInstance_IFamilyManager_IFamilyType, m_itemMap, pFamilyType != nullptr);
                pQTreeItemFamilyType->setData(2, 0, QVariant::fromValue(QString::fromStdWString(pFamilyType->GetName())));
            }

            QTreeWidgetItem* pQTreeItemElemParams = CreateTreeItem(pQTreeItemFamilyMgr, L"IElementParameters", ElementParts::IInstance_IElementParameters, m_itemMap, true);
            const gcmp::IElementParameters* pElemParameters = pFamMgr->GetElementParameters();
            const std::vector<OwnerPtr<gcmp::IParameter>> opParams = pElemParameters->GetAllParameters();
            for (const auto& opParam : opParams)
            {
                QTreeWidgetItem* pQTreeItemParam = CreateTreeItem(pQTreeItemElemParams, L"IParameter_" + opParam->GetName(), ElementParts::IInstance_IElementParameters_IParameter, m_itemMap, true);
                pQTreeItemParam->setData(2, 0, QVariant::fromValue(opParam->GetParameterDefinitionId()));
            }
            QTreeWidgetItem* pQTreeItemFamParamDef = CreateTreeItem(pQTreeItemFamilyMgr, L"IFamilyParameterDefinition", ElementParts::IInstance_IFamilyParameterDefinition, m_itemMap, true);
            const std::vector<ElementId> famParamIds = pFamMgr->GetFamilyParameterDefinitionIds();
            for (const ElementId& id : famParamIds)
            {
                const gfam::IFamilyParameterDefinition* pFamParamDef = pFamMgr->GetFamilyParameterDefinition(id);
                QTreeWidgetItem* pQTreeItemParam = CreateTreeItem(pQTreeItemFamParamDef, L"IParameter_" + pFamParamDef->GetName(), ElementParts::IInstance_IFamilyParameterDefinition_IParameter, m_itemMap, true);
                pQTreeItemParam->setData(2, 0, QVariant::fromValue(QString::fromStdWString(pFamParamDef->GetName())));
            }
            IDocumentManager::Get()->CloseDocument(pFamDoc->GetRuntimeId());
        }
    }
}

// 点击节点时在GDMP对象浏览器显示信息
void Sample::ElementProbeDlg::OnTreeItemSelected()
{
    ObjectBrowser* pObjectBrowser = ObjectBrowser::Get();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pObjectBrowser, L"pObjectBrowser为空",L"GDMPLab",L"2024-12-30");
    pObjectBrowser->SetEnabled(true);

    IDocument* pDoc = UiDocumentViewUtils::GetCurrentDocument();
    if (nullptr == pDoc) { return; }
    const gcmp::IElement* pElem = pDoc->GetElement(m_elementId);
    if (nullptr == pElem) { return; }

    QList<QTreeWidgetItem*> selected = m_pQTreeWidget->selectedItems();
    if (selected.count() == 0)
    {
        return;
    }

    QTreeWidgetItem* item = selected.at(0);
    int elemParts = item->data(1, 0).toInt();

    m_opCurrentViewWrapper = NEW_AS_OWNER_PTR(ElementWrapper, m_elementId);

    if (elemParts == ElementParts::IElement)
    {
        m_opCurrentViewWrapper->CreateParam_IElement();
    }
    else if (elemParts == ElementParts::IElementBasicInformation)
    {
        m_opCurrentViewWrapper->CreateParam_IElementBasicInformation();
    }
    else if (elemParts == ElementParts::IElementStatus)
    {
        m_opCurrentViewWrapper->CreateParam_IElementStatus();
    }
    else if (elemParts == ElementParts::ILayerComponent)
    {
        m_opCurrentViewWrapper->CreateParam_ILayerComponent();
    }
    else if (elemParts == ElementParts::IElementTags)
    {
        m_opCurrentViewWrapper->CreateParam_IElementTags();
    }
    else if (elemParts == ElementParts::IElementAttributes)
    {
        m_opCurrentViewWrapper->CreateParam_IElementAttributes();
    }
    else if (elemParts == ElementParts::IExternalDataComponent)
    {
        m_opCurrentViewWrapper->CreateParam_IExternalDataComponent(); 
    }
    else if (elemParts == ElementParts::IExternalObject)
    {
        m_opCurrentViewWrapper->CreateParam_IExternalObject();
    }
    else if (elemParts == ElementParts::IElementParameters)
    {
        m_opCurrentViewWrapper->CreateParam_IElementParameters();
    }
    else if (elemParts == ElementParts::IParameter)
    {
        const gcmp::IElementParameters* pElemParams = pElem->GetElementParameters();
        if (pElemParams)
        {
            int paramId = item->data(2, 0).toInt();
            OwnerPtr<gcmp::IParameter> opParam = pElemParams->GetParameterById(paramId);
            m_opCurrentViewWrapper->CreateParam_IParameter(opParam);
        }
    }
    else if (elemParts == ElementParts::IElementParameters_IElementParametersCustomizer)
    {
        const gcmp::IElementParameters* pElemParams = pElem->GetElementParameters();
        const gcmp::IElementParametersCustomizer* pParamCustomizer = pElemParams->GetParametersCustomizer();
        if (pParamCustomizer)
        {
            m_opCurrentViewWrapper->CreateParam_IElementParametersCustomizer(pParamCustomizer);
        }
    }
    else if (elemParts == ElementParts::IElementParameters_IElementParameterBindings)
    {
        const gcmp::IElementParameters* pElemParams = pElem->GetElementParameters();
        const gcmp::IElementParameterBindings* pParamBindings = pElemParams->GetElementParameterBindings();
        if (pParamBindings)
        {
            m_opCurrentViewWrapper->CreateParam_IElementParameterBindings(pParamBindings);
        }
    }
    else if (elemParts == ElementParts::IElementParameters_IElementParametersCustomizer_IParameter)
    {
        const gcmp::IElementParameters* pElemParams = pElem->GetElementParameters();
        if (pElemParams)
        {
            int paramId = item->data(2, 0).toInt();
            const gcmp::IElementParametersCustomizer* pParamCustomizer = pElemParams->GetParametersCustomizer();
            OwnerPtr<gcmp::IParameter> opParam = pElemParams->GetParameterById(paramId);
            m_opCurrentViewWrapper->CreateParam_IParameter(opParam);
        }
    }
    else if (elemParts == ElementParts::IElementParameters_IElementParametersCustomizers_IParameter)
    {
        const gcmp::IElementParameters* pElemParams = pElem->GetElementParameters();
        if (pElemParams)
        {
            GUID guid = GuidUtils::FromString(item->data(2, 0).toString().toStdWString());
            int paramId = item->data(3, 0).toInt();
            const gcmp::IElementParametersCustomizer* pParamCustomizer = pElemParams->GetParametersCustomizer(guid);
            OwnerPtr<gcmp::IParameter> opParam = pElemParams->GetParameterById(paramId);
            m_opCurrentViewWrapper->CreateParam_IParameter(opParam);
        }
    }
    else if (elemParts == ElementParts::EmbeddedParametersMap_IParameter)
    {
        const gcmp::IElementParameters* pElemParams = pElem->GetElementParameters();
        if (pElemParams)
        {
            int paramId = item->data(2, 0).toInt();
            OwnerPtr<gcmp::IParameter> opParam = pElemParams->GetNativeParameter(paramId);
            m_opCurrentViewWrapper->CreateParam_IParameter(opParam);
        }
    }
    else if (elemParts == ElementParts::IElementParameterGroupOverride)
    {
        const gcmp::IElementParameters* pElemParams = pElem->GetElementParameters();
        if (pElemParams)
        {
            const gcmp::IElementParameterGroupOverride* pParamGroupOverride = pElemParams->GetElementParameterGroupOverride();
            if (pParamGroupOverride)
            {
                m_opCurrentViewWrapper->CreateParam_IElementParameterGroupOverride();
            }
        }
    }
    else if (elemParts == ElementParts::IElementRegenerationComponent)
    {
        const gcmp::IElementRegenerationComponent* pElemRegen = pElem->GetElementRegenerationComponent();
        if (pElemRegen)
        {
            m_opCurrentViewWrapper->CreateParam_IElementRegenerationComponent();
        }
    }
    else if (elemParts == ElementParts::IExternalData)
    {
        const gcmp::IExternalDataComponent* pDataComponent = pElem->GetExternalDataComponent();
        if (pDataComponent)
        {
            const std::vector<OwnerPtr<gcmp::IExternalData>>& exDatas = pDataComponent->GetAllExternalDatas();
            int dataCount = (int)exDatas.size();
            int dataIndex = item->data(2, 0).toInt();
            if (dataIndex < dataCount)
            {
                const OwnerPtr<gcmp::IExternalData>& opExData = exDatas[dataIndex];
                m_opCurrentViewWrapper->CreateParam_IExternalData(opExData);
            }
        }
    }
    else if (elemParts == ElementParts::IExternalData_IParameter)
    {
        const gcmp::IExternalDataComponent* pDataComponent = pElem->GetExternalDataComponent();
        if (pDataComponent)
        {
            const std::vector<OwnerPtr<gcmp::IExternalData>>& exDatas = pDataComponent->GetAllExternalDatas();
            int dataCount = (int)exDatas.size();
            int dataIndex = item->data(2, 0).toInt();
            int paramId = item->data(3, 0).toInt();
            if (dataIndex < dataCount)
            {
                const OwnerPtr<gcmp::IExternalData>& opExData = exDatas[dataIndex];
                OwnerPtr<gcmp::IParameter> opParam = opExData->GetNativeParameter(paramId);
                m_opCurrentViewWrapper->CreateParam_IParameter(opParam);
            }
        }
        const std::vector<OwnerPtr<gcmp::IExternalData>>& datas = pDataComponent->GetAllExternalDatas();
    }
    else if (elemParts == ElementParts::IElementDeletionComponent)
    {
        m_opCurrentViewWrapper->CreateParam_IElementDeletionComponent();
    }
    else if (elemParts == ElementParts::IElementTransformationComponent)
    {
        m_opCurrentViewWrapper->CreateParam_IElementTransformationComponent();
    }
    else if (elemParts == ElementParts::IElementCopyPasteComponent)
    {
        m_opCurrentViewWrapper->CreateParam_IElementCopyPasteComponent();
    }
    else if (elemParts == ElementParts::IElementCopyStrategyComponent)
    {
        m_opCurrentViewWrapper->CreateParam_IElementCopyStrategyComponent();
    }
    else if (elemParts == ElementParts::IGroupComponent)
    {
        m_opCurrentViewWrapper->CreateParam_IGroupComponent();
    }
    else if (elemParts == ElementParts::IElementPosition)
    {
        m_opCurrentViewWrapper->CreateParam_IElementPosition();
    }
    else if (elemParts == ElementParts::IPositionGeometry)
    {
        m_opCurrentViewWrapper->CreateParam_IPositionGeometry();
    }
    else if (elemParts == ElementParts::IPositionAssociatedPlane)
    {
        m_opCurrentViewWrapper->CreateParam_IPositionAssociatedPlane();
    }
    else if (elemParts == ElementParts::IElementPositionPoints)
    {
        m_opCurrentViewWrapper->CreateParam_IElementPositionPoints();
    }
    else if (elemParts == ElementParts::IElementPositionReportComponent)
    {
        m_opCurrentViewWrapper->CreateParam_IElementPositionReportComponent();
    }
    else if (elemParts == ElementParts::IElementModelShape)
    {
        m_opCurrentViewWrapper->CreateParam_IElementModelShape();
    }
    else if (elemParts == ElementParts::IGraphicsNodeStyleAndMaterialOverride)
    {
        m_opCurrentViewWrapper->CreateParam_IGraphicsNodeStyleAndMaterialOverride();
    }
    else if (elemParts == ElementParts::IElementModelingOperations)
    {
        m_opCurrentViewWrapper->CreateParam_IElementModelingOperations();
    }
    else if (elemParts == ElementParts::IGeometryRelationshipComponent)
    {
        m_opCurrentViewWrapper->CreateParam_IGeometryRelationshipComponent();
    }
    else if (elemParts == ElementParts::IGeometryRelationshipComponent_IOpeningRelationshipBehavior)
    {
        m_opCurrentViewWrapper->CreateParam_IOpeningRelationshipBehavior();
    }
    else if (elemParts == ElementParts::IGeometryRelationshipComponent_IOpenedGraphicsElementShapeComponent)
    {
        m_opCurrentViewWrapper->CreateParam_IOpenedGraphicsElementShapeComponent();
    }
    else if (elemParts == ElementParts::IGeometryRelationshipComponent_IOpeningGraphicsElementShapeComponent)
    {
        m_opCurrentViewWrapper->CreateParam_IOpeningGraphicsElementShapeComponent();
    }
    else if (elemParts == ElementParts::IGeometryRelationshipComponent_ICutRelationshipBehavior)
    {
        m_opCurrentViewWrapper->CreateParam_ICutRelationshipBehavior();
    }
    else if (elemParts == ElementParts::IGeometryRelationshipComponent_ICutterGraphicsElementShapeComponent)
    {
        m_opCurrentViewWrapper->CreateParam_ICutterGraphicsElementShapeComponent();
    }
    else if (elemParts == ElementParts::IGeometryRelationshipComponent_ICutteeGraphicsElementShapeComponent)
    {
        m_opCurrentViewWrapper->CreateParam_ICutteeGraphicsElementShapeComponent();
    }
    else if (elemParts == ElementParts::IGeometryRelationshipComponent_IJoinRelationshipBehavior)
    {
        m_opCurrentViewWrapper->CreateParam_IJoinRelationshipBehavior();
    }
    else if (elemParts == ElementParts::IGeometryRelationshipComponent_IGeometryRelationship)
    {
        int dataIndex = item->data(2, 0).toInt();
        m_opCurrentViewWrapper->CreateParam_IGeometryRelationship(dataIndex);
    }
    else if (elemParts == ElementParts::IElementConstraintBehavior)
    {
        m_opCurrentViewWrapper->CreateParam_IElementConstraintBehavior();
    }
    else if (elemParts == ElementParts::ICommandBehavior)
    {
        m_opCurrentViewWrapper->CreateParam_ICommandBehavior();
    }
    else if (elemParts == ElementParts::IInstance)
    {
        m_opCurrentViewWrapper->CreateParam_IInstance();
    }
    else if (elemParts == ElementParts::IInstance_IInstanceType)
    {
        m_opCurrentViewWrapper->CreateParam_IInstance_IInstanceType();
    }
    else if (elemParts == ElementParts::IInstance_IFamily)
    {
        m_opCurrentViewWrapper->CreateParam_IInstance_IFamily();
    }
    else if (elemParts == ElementParts::IInstance_IFamilyManager)
    {
        m_opCurrentViewWrapper->CreateParam_IInstance_IFamilyManager();
    }
    else if (elemParts == ElementParts::IInstance_IFamilyConfigElement)
    {
        m_opCurrentViewWrapper->CreateParam_IInstance_IFamilyConfigElement();
    }
    else if (elemParts == ElementParts::IInstance_IElementParameters)
    {
        const gcmp::IInstance* pInst = quick_cast<gcmp::IInstance>(pElem);
        const gcmp::IFamily* pFamily = pInst->GetFamily();
        const IFamilyConfigElement* pIFamilyConfigElement = IFamilyConfigElement::GetFamilyConfigElementByName(pDoc, pFamily->GetBasicInformation()->GetName());

        std::wstring familyFilePath = pIFamilyConfigElement->GetFamilyFilePath(pDoc);
        IDocumentManager* pDocManager = IDocumentManager::Get();
        DocumentLoadOptions docLoadOptions(false, false, true, false);
        DocumentLoadResult docLoadResult;
        std::set<std::wstring> unrecognizedAssemblies;
        IDocument* pFamDoc = pDocManager->OpenDocument(familyFilePath, docLoadOptions, docLoadResult, nullptr, &unrecognizedAssemblies);
        const gfam::IFamilyManager* pFamMgr = gfam::IFamilyManager::Get(pFamDoc);
        const gcmp::IElementParameters* pElemParams = pFamMgr->GetElementParameters();
        m_opCurrentViewWrapper->CreateParam_IInstance_IElementParameters(pElemParams);
        IDocumentManager::Get()->CloseDocument(pFamDoc->GetRuntimeId());
    }
    else if (elemParts == ElementParts::IInstance_IElementParameters_IParameter)
    {
        int defId = item->data(2, 0).toInt();
        const gcmp::IInstance* pInst = quick_cast<gcmp::IInstance>(pElem);
        const gcmp::IFamily* pFamily = pInst->GetFamily();
        const IFamilyConfigElement* pIFamilyConfigElement = IFamilyConfigElement::GetFamilyConfigElementByName(pDoc, pFamily->GetBasicInformation()->GetName());

        std::wstring familyFilePath = pIFamilyConfigElement->GetFamilyFilePath(pDoc);
        IDocumentManager* pDocManager = IDocumentManager::Get();
        DocumentLoadOptions docLoadOptions(false, false, true, false);
        DocumentLoadResult docLoadResult;
        std::set<std::wstring> unrecognizedAssemblies;
        IDocument* pFamDoc = pDocManager->OpenDocument(familyFilePath, docLoadOptions, docLoadResult, nullptr, &unrecognizedAssemblies);
        const gfam::IFamilyManager* pFamMgr = gfam::IFamilyManager::Get(pFamDoc);
        const gcmp::IElementParameters* pElemParams = pFamMgr->GetElementParameters();
        OwnerPtr<gcmp::IParameter> opParam = pElemParams->GetParameterById(defId);
        m_opCurrentViewWrapper->CreateParam_IInstance_IElementParameters_IParameter(TransferOwnership(opParam));
        IDocumentManager::Get()->CloseDocument(pFamDoc->GetRuntimeId());
    }
    else if (elemParts == ElementParts::IInstance_IFamilyManager_IFamilyType)
    {
        std::wstring famTypeName = item->data(2, 0).toString().toStdWString();
        const gcmp::IInstance* pInst = quick_cast<gcmp::IInstance>(pElem);
        const gcmp::IFamily* pFamily = pInst->GetFamily();
        const IFamilyConfigElement* pIFamilyConfigElement = IFamilyConfigElement::GetFamilyConfigElementByName(pDoc, pFamily->GetBasicInformation()->GetName());

        std::wstring familyFilePath = pIFamilyConfigElement->GetFamilyFilePath(pDoc);
        IDocumentManager* pDocManager = IDocumentManager::Get();
        DocumentLoadOptions docLoadOptions(false, false, true, false);
        DocumentLoadResult docLoadResult;
        std::set<std::wstring> unrecognizedAssemblies;
        IDocument* pFamDoc = pDocManager->OpenDocument(familyFilePath, docLoadOptions, docLoadResult, nullptr, &unrecognizedAssemblies);
        const gfam::IFamilyManager* pFamMgr = gfam::IFamilyManager::Get(pFamDoc);
        const IFamilyType* pFamType = pFamMgr->GetFamilyTypeByName(famTypeName);
        m_opCurrentViewWrapper->CreateParam_IInstance_IFamilyManager_IFamilyType(pFamType, pFamMgr);
        IDocumentManager::Get()->CloseDocument(pFamDoc->GetRuntimeId());
    }
    else if (elemParts == ElementParts::IInstance_IFamilyParameterDefinition)
    {
    }
    else if (elemParts == ElementParts::IInstance_IFamilyParameterDefinition_IParameter)
    {
        std::wstring defName = item->data(2, 0).toString().toStdWString();
        const gcmp::IInstance* pInst = quick_cast<gcmp::IInstance>(pElem);
        const gcmp::IFamily* pFamily = pInst->GetFamily();
        const IFamilyConfigElement* pIFamilyConfigElement = IFamilyConfigElement::GetFamilyConfigElementByName(pDoc, pFamily->GetBasicInformation()->GetName());

        std::wstring familyFilePath = pIFamilyConfigElement->GetFamilyFilePath(pDoc);
        IDocumentManager* pDocManager = IDocumentManager::Get();
        DocumentLoadOptions docLoadOptions(false, false, true, false);
        DocumentLoadResult docLoadResult;
        std::set<std::wstring> unrecognizedAssemblies;
        IDocument* pFamDoc = pDocManager->OpenDocument(familyFilePath, docLoadOptions, docLoadResult, nullptr, &unrecognizedAssemblies);
        const gfam::IFamilyManager* pFamMgr = gfam::IFamilyManager::Get(pFamDoc);
        const gfam::IFamilyParameterDefinition* pFamParamDef = pFamMgr->GetFamilyParameterDefinition(defName);
        m_opCurrentViewWrapper->CreateParam_IInstance_IFamilyParameterDefinition_IParameter(pFamDoc, pFamParamDef);
        IDocumentManager::Get()->CloseDocument(pFamDoc->GetRuntimeId());
    }
    else if (elemParts == ElementParts::IInstance_IFamily_IFamilyType)
    {
        const gcmp::IInstance* pInst = quick_cast<gcmp::IInstance>(pElem);
        const gcmp::IFamily* pFamily = pInst->GetFamily();
        {
            std::wstring famTypeName = item->data(2, 0).toString().toStdWString();
            const IFamilyType* pFamType = pFamily->GetFamilyTypeByName(famTypeName);
            m_opCurrentViewWrapper->CreateParam_IInstance_IFamily_IFamilyType(pFamType);
        }
    }
    else if (elemParts == ElementParts::IInstance_IFamily_InstanceType)
    {
        const gcmp::IInstance* pInst = quick_cast<gcmp::IInstance>(pElem);
        const gcmp::IFamily* pFamily = pInst->GetFamily();
        {
            std::wstring instTypeName = item->data(2, 0).toString().toStdWString();
            const gcmp::IInstanceType* pInstType = pFamily->GetInstanceTypeByName(instTypeName);
            m_opCurrentViewWrapper->CreateParam_IInstance_IFamily_InstanceType(pInstType);
        }
    }

    if(m_opCurrentViewWrapper)
    {
        pObjectBrowser->SetDataAndUpdate(m_opCurrentViewWrapper.get());
    }
    else
    {
        pObjectBrowser->SetDataAndUpdate(nullptr);
    }

    // 点击【应用】按钮，回车输入，鼠标开开GDMP对象浏览器，或其他触发GDMP对象浏览器的更新
    pObjectBrowser->SetOnApplyCallBack(ObjectBrowser::CreateOnApplyCallBack(&ElementProbeDlg::UpdateObjectWrapperChanges, this));
}

void Sample::ElementProbeDlg::UpdateObjectWrapperChanges(ObjectWrapper* gObjectWrapper)
{
    if (m_opCurrentViewWrapper == nullptr)
    {
        return;
    }

    IUiDocumentViewManager* pUiDocViewMgr = IUiDocumentViewManager::Get();
    IUiDocument* pUiDoc = pUiDocViewMgr->GetCurrentUiDocument();
    IDocument* pDoc = pUiDoc->GetDbDocument();

    IUiView* pCurrentView = pUiDocViewMgr->GetCurrentUiView();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pCurrentView, L"pCurrentView为空",L"GDMPLab",L"2024-12-30");

    IMainWindow* pMainWindow = IMainWindow::GetMainWindow();
    QList<QTreeWidgetItem*> selected = m_pQTreeWidget->selectedItems();
    if (selected.count() == 0)
    {
        return;
    }

    QTreeWidgetItem* item = selected.at(0);
    int settingType = item->data(1, 0).toInt();

    pMainWindow->UpdateControlStatus();
}

void Sample::ElementProbeDlg::SetElementId(ElementId id)
{
    m_elementId = id;
    AddNodes();
    m_pQTreeWidget->expandAll();
}

#pragma endregion 视图设置相关的逻辑

