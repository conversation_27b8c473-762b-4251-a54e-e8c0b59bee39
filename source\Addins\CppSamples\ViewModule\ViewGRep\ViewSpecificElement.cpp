﻿#include "ViewSpecificElement.h"
#include "IBody.h"
#include "IArc2d.h"
#include "ILine3d.h"
#include "IPolygon.h"
#include "IInstance.h"
#include "IDocument.h"
#include "IModelView.h"
#include "IPolyCurve.h"
#include "IUiDocument.h"
#include "IArc3d.h"
#include "ILine3d.h"
#include "IMesh.h"
#include "IGraphicsCurve3d.h"
#include "DbObjectUtils.h"
#include "GmBodyBuilder.h"
#include "Vector3dUtils.h"
#include "UiCommonDialog.h"
#include "IGenericElement.h"
#include "IElementPosition.h"
#include "IUserTransaction.h"
#include "GcmpCommandState.h"
#include "IGraphicsBRepBody.h"
#include "IGraphicsMeshBody.h"
#include "IElementModelShape.h"
#include "IGraphicsNodeGroup.h"
#include "IGraphicsCurve3dList.h"
#include "AlgorithmMeshOperate.h"
#include "IGraphicsElementShape.h"
#include "IUiDocumentViewManager.h"
#include "IElementParentReporter.h"
#include "IGraphicsNodeReference.h"
#include "IElementTransformationComponent.h"
#include "GcmpBuiltInCategoryUniIdentities.h"

#include "UiDocumentViewUtils.h"
#include "IUiView.h"
#include "ICanvas.h"
#include "Color.h"
#include "IGraphicsStyleData.h"
#include "IGraphicsStyle.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;
using namespace Sample;

// 简单的图元变换组件
namespace Sample
{
    class SampleViewSpecificElementTransformationComponent : public gcmp::NdbObjectSchematic, public gcmp::IElementTransformationComponent
    {
        DBOBJECT_DATA_BEGIN_AND_QUICK_CAST_FROM(Sample, SampleViewSpecificElementTransformationComponent, gcmp::NdbObjectSchematic, BFC67FDF - 7ED1 - 44AA - B715 - 76783DEB9316, ViewModule, gcmp::IElementTransformationComponent)
            DATA_TRANSIENT(gcmp::IElement*, pOwnerElement)
            DBOBJECT_DATA_END
    public:
        virtual bool Translate(const IElementMoveContext& moveContext) override
        {
            const Vector3d& translation = moveContext.GetMoveVector();
            if (translation.IsZero())
            {
                return true;
            }

            Matrix4d translateMatrix;
            translateMatrix.MakeTranslate(translation.X(), translation.Y(), translation.Z());
            return Transform(translateMatrix);
        }

        virtual bool Rotate(const Vector3d& pointOnVec, const Vector3d& rotateDir, double rotateAngle) override
        {
            const double num2PI = std::floor(rotateAngle / gcmp::Constants::Constants::MATH_2PI);
            const double angleIn2PI = rotateAngle - num2PI * gcmp::Constants::Constants::MATH_2PI;
            if (MathUtils::IsZero(angleIn2PI))
            {
                return true;
            }

            Matrix4d rotationMatrix;
            rotationMatrix.MakeRotate(pointOnVec, rotateDir, rotateAngle);
            return Transform(rotationMatrix);
        }

        virtual bool Transform(const Matrix4d& matrix) override
        {
            if (matrix.IsIdentity())
            {
                return true;
            }

            IElement* pElement = GetOwnerElement();
            DBG_WARN_AND_RETURN_FALSE_UNLESS(pElement, L"pElement为空", L"GDMPLab", L"2024-03-30");
            IGenericElement* pGenericElement = quick_cast<IGenericElement>(pElement);
            DBG_WARN_AND_RETURN_FALSE_UNLESS(pGenericElement, L"pGenericElement为空", L"GDMPLab", L"2024-03-30");
            // 视图组件实例图元
            SampleViewSpecificElement* pViewSpecificElement = quick_cast<SampleViewSpecificElement>(pGenericElement->GetExternalObject());
            DBG_WARN_AND_RETURN_FALSE_UNLESS(pViewSpecificElement, L"pViewSpecificElement为空", L"GDMPLab", L"2024-03-30");
            pViewSpecificElement->Transform(matrix);

            IElementPosition* pPositionBehavior = pElement->GetElementPosition();
            DBG_WARN_AND_RETURN_FALSE_UNLESS(pPositionBehavior, L"pPositionBehavior为空", L"GDMPLab", L"2024-03-30");

            return pPositionBehavior->Transform(matrix);
        }

        virtual NdbObject* GetTopOwnerObject() const override
        {
            return quick_cast<gcmp::NdbObject>(m_pOwnerElement);
        }

        virtual bool CanBeMoved(std::wstring* explanation = nullptr) const override
        {
            return true;
        }

        virtual bool CanBeRotated(std::wstring* explanation = nullptr) const override
        {
            return true;
        }

        virtual bool CanBeScaled(std::wstring* explanation = nullptr) const override
        {
            return false;
        }

        virtual bool Scale(const Vector3d& basePt, float scaleFactor) override
        {
            return false;
        }

        virtual IElement* GetOwnerElement() override
        {
            return m_pOwnerElement;
        }

        virtual const IElement* GetOwnerElement() const override
        {
            return m_pOwnerElement;
        }

        virtual bool SetOwnerElement(IElement* pOwnerElement) override
        {
            m_pOwnerElement = pOwnerElement;
            return true;
        }
    };
}

DBOBJECT_DATA_DEFINE(SampleViewSpecificElementTransformationComponent)
{
    m_pOwnerElement = nullptr;
}

DBOBJECT_DATA_DEFINE(SampleGraphicsNodeVisibilityCustomizer)
{
    m_pOwnerElement = nullptr;
}

IGraphicsNodeVisibilityCustomizer::CustomizationInfo SampleGraphicsNodeVisibilityCustomizer::GetCustomizationInfo(const IModelView * pModelView) const
{
    IGraphicsNodeVisibilityCustomizer::CustomizationInfo info;
    const IElement* pConstOwnerElement = m_pOwnerElement;
    DBG_WARN_AND_RETURN_UNLESS(pConstOwnerElement, info, L"pConstOwnerElement为空", L"GDMPLab", L"2024-03-30");
    OwnerPtr<IGraphicsNodeReference> opNodeRef0 = IGraphicsNodeReference::Create(pConstOwnerElement->GetElementId(),
        { GraphicsNodeId(101) }, GraphicsNodeReferenceType::GraphicsNodeSelf);
    //OwnerPtr<IGraphicsNodeReference> opNodeRef1 = IGraphicsNodeReference::Create(pConstOwnerElement->GetElementId(),
    //{ GraphicsNodeId(102) }, GraphicsNodeReferenceType::GraphicsNodeSelf);
    //OwnerPtr<IGraphicsNodeReference> opNodeRef2 = IGraphicsNodeReference::Create(pConstOwnerElement->GetElementId(),
    //{ GraphicsNodeId(103) }, GraphicsNodeReferenceType::GraphicsNodeSelf);
    //OwnerPtr<IGraphicsNodeReference> opNodeRef3 = IGraphicsNodeReference::Create(pConstOwnerElement->GetElementId(),
    //{ GraphicsNodeId(104) }, GraphicsNodeReferenceType::GraphicsNodeSelf);
    OwnerPtr<IGraphicsNodeReference> opNodeRef4 = IGraphicsNodeReference::Create(pConstOwnerElement->GetElementId(),
    { GraphicsNodeId(105) }, GraphicsNodeReferenceType::GraphicsNodeSelf);

    IUiView* pUiView = UiDocumentViewUtils::GetCurrentUiView();
    ICanvas* pCanvas = pUiView->GetCanvas();

    if (pCanvas->GetBackgroundColor() == Color::Black)
    {
        info.CustomizeElementVisibilityByGraphicsNodeReference(*opNodeRef0, GraphicsNodeVisibility::NeverHighlighted_Or_Selected);
        //info.CustomizeElementVisibilityByGraphicsNodeReference(*opNodeRef1, GraphicsNodeVisibility::Always);
        //info.CustomizeElementVisibilityByGraphicsNodeReference(*opNodeRef2, GraphicsNodeVisibility::Always);
        //info.CustomizeElementVisibilityByGraphicsNodeReference(*opNodeRef3, GraphicsNodeVisibility::Always);
        //info.CustomizeElementVisibilityByGraphicsNodeReference(*opNodeRef4, GraphicsNodeVisibility::NeverHighlighted_Or_Selected);
    }
    else
    {
        info.CustomizeElementVisibilityByGraphicsNodeReference(*opNodeRef0, GraphicsNodeVisibility::HighlightedExactly_Or_SelectedExactly_Or_RootGRepHighlightedOrSelected);
        //info.CustomizeElementVisibilityByGraphicsNodeReference(*opNodeRef1, GraphicsNodeVisibility::HighlightedExactly_Or_SelectedExactly_Or_RootGRepHighlightedOrSelected);
        //info.CustomizeElementVisibilityByGraphicsNodeReference(*opNodeRef2, GraphicsNodeVisibility::HighlightedExactly_Or_SelectedExactly_Or_RootGRepHighlightedOrSelected);
        //info.CustomizeElementVisibilityByGraphicsNodeReference(*opNodeRef3, GraphicsNodeVisibility::HighlightedExactly_Or_SelectedExactly_Or_RootGRepHighlightedOrSelected);
        //info.CustomizeElementVisibilityByGraphicsNodeReference(*opNodeRef4, GraphicsNodeVisibility::HighlightedExactly_Or_SelectedExactly_Or_RootGRepHighlightedOrSelected);
    }

    return info;
}

DBOBJECT_DATA_DEFINE(SampleViewSpecificElement)
{
    m_pOwnerElement = nullptr;
}

NdbObject* SampleViewSpecificElement::GetTopOwnerObject() const
{
    return quick_cast<gcmp::NdbObject>(m_pOwnerElement);
}

void SampleViewSpecificElement::ReportParents(IElementParentReporter& elementParentReporter) const
{
    const IElement* pElement = GetOwnerElement();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pElement, L"IElement为空", L"GDMPLab", L"2024-03-30");
    elementParentReporter.ReportStrong(pElement->GetElementId());
}

void SampleViewSpecificElement::UpdateForWeakParentDeletion(const std::set<ElementId>& deletedElementIds)
{
    return;
}

bool SampleViewSpecificElement::Transform(const Matrix4d& matrix)
{
    IElement* pElement = GetOwnerElement();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pElement, L"pElement为空", L"GDMPLab", L"2024-03-30");
    IElementModelShape* pModelShape = pElement->GetElementModelShape();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pModelShape, L"pModelShape为空", L"GDMPLab", L"2024-03-30");
    OwnerPtr<IGraphicsElementShape> opGraphicsElementShape = pModelShape->TransferGraphicsElementShape();
    DBG_WARN_AND_RETURN_FALSE_UNLESS(opGraphicsElementShape, L"opGraphicsElementShape为空", L"GDMPLab", L"2024-03-30");
    opGraphicsElementShape->Transform(matrix);
    pModelShape->SetGraphicsElementShape(TransferOwnership(opGraphicsElementShape));
    return true;
}

SampleViewSpecificElement* SampleViewSpecificElement::Create(gcmp::IDocument* pDocument, gcmp::ElementCreationOptions eco /*= gcmp::ElementCreationOptions::Normal*/)
{
    OwnerPtr<SampleViewSpecificElement> opViewSpecificElem = NEW_AS_OWNER_PTR(SampleViewSpecificElement);
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(opViewSpecificElem, L"创建opViewSpecificElem失败", L"GDMPLab", L"2024-03-30");

    SampleViewSpecificElement* pViewSpecificElem = opViewSpecificElem.get();

    IGenericElement* pGenericElement = IGenericElement::Create(
        pDocument, TransferOwnership(opViewSpecificElem),
        SampleViewSpecificElement::GetClassId().GetGuid(),
        BuiltInCategoryUniIdentities::BICU_FORM,
        ElementCreationOptions::Normal);
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pGenericElement, L"创建IGenericElement失败", L"GDMPLab", L"2024-03-30");

    Coordinate3d coord;
    IElementPosition::CreatePoint2dOnAssociatedCoordinatePositionBehavior(pGenericElement, coord, Vector3d::Zero, Vector3d::UnitX, Vector3d::UnitY);

    OwnerPtr<ViewSpecificShapeCustomComponent> opViewSpecificBehavior = NEW_AS_OWNER_PTR(ViewSpecificShapeCustomComponent);
    ViewSpecificShapeCustomComponent* pViewSpecificBehavior = opViewSpecificBehavior.get();
    pGenericElement->SetViewSpecificShapeComponent(TransferOwnership(opViewSpecificBehavior));
    pViewSpecificBehavior->SetOwnerElement(pGenericElement);
    pViewSpecificBehavior->SetGraphicsNodeVisibilityCustomizer(SampleGraphicsNodeVisibilityCustomizer::Create(pGenericElement));

    OwnerPtr<SampleViewSpecificElementTransformationComponent> opTransformComponent = NEW_AS_OWNER_PTR(SampleViewSpecificElementTransformationComponent);
    pGenericElement->SetElementTransformationComponent(TransferOwnership(opTransformComponent));

    // 创建门的两个拉伸体
    //std::vector<Vector3d> points1 = { Vector3d::Zero, Vector3d(800,0,0), Vector3d(800,100,0), Vector3d(0,100,0) };
    //std::vector<Vector3d> points2 = { Vector3d::Zero, Vector3d(0,100,0), Vector3d(-800,100,0), Vector3d(-800,0,0) };
    //OwnerPtr<IBody> opBody1 = GmBodyBuilder::CreateExtrudeBodyByPoints(points1, 2000);
    //OwnerPtr<IBody> opBody2 = GmBodyBuilder::CreateExtrudeBodyByPoints(points2, 2000);
    //OwnerPtr<IGraphicsBRepBody> opBRepBody1 = IGraphicsBRepBody::Create(TransferOwnership(opBody1));
    //OwnerPtr<IGraphicsBRepBody> opBRepBody2 = IGraphicsBRepBody::Create(TransferOwnership(opBody2));
    //opBRepBody1->SetId(GraphicsNodeId(10));
    //opBRepBody2->SetId(GraphicsNodeId(11));
    gcmp::OwnerPtr<IGraphicsElementShape> opGRep = IGraphicsElementShape::Create(GraphicsRenderLayer::Model);
    //opGRep->AddChild(TransferOwnership(opBRepBody1));
    //opGRep->AddChild(TransferOwnership(opBRepBody2));

    //gcmp::OwnerPtr<IGraphicsNodeGroup> opGGroup = IGraphicsNodeGroup::Create();
    //opGGroup->SetId(GraphicsNodeId(100));

    //OwnerPtr<IGraphicsCurve3d> opDoorLine1 = IGraphicsCurve3d::Create(ILine3d::Create(Vector3d(800, 0, 0), Vector3d(800, 800, 0)).get());
    //opDoorLine1->SetId(GraphicsNodeId(101));
    //opGRep->AddChild(TransferOwnership(opDoorLine1));

    //OwnerPtr<IGraphicsCurve3d> opDoorLine2 = IGraphicsCurve3d::Create(ILine3d::Create(Vector3d(-800, 0, 0), Vector3d(-800, 800, 0)).get());
    //opDoorLine2->SetId(GraphicsNodeId(102));
    //opGRep->AddChild(TransferOwnership(opDoorLine2));

    //OwnerPtr<IGraphicsCurve3d> opDoorArc1 = IGraphicsCurve3d::Create(IArc3d::Create(Vector3d(800, 0, 0), Vector3d(800, 800, 0), Vector3d::Zero, true).get());
    //opDoorArc1->SetId(GraphicsNodeId(103));
    //opGRep->AddChild(TransferOwnership(opDoorArc1));

    //OwnerPtr<IGraphicsCurve3d> opDoorArc2 = IGraphicsCurve3d::Create(IArc3d::Create(Vector3d(-800, 0, 0), Vector3d(-800, 800, 0), Vector3d::Zero, true).get());
    //opDoorArc2->SetId(GraphicsNodeId(104));
    //opGRep->AddChild(TransferOwnership(opDoorArc2));

    std::vector<OwnerPtr<ICurve3d>> opCrvs;
    OwnerPtr<ICurve3d> opDoorLine1 = ILine3d::Create(Vector3d(800, 0, 0), Vector3d(800, 800, 0));
    opCrvs.emplace_back(TransferOwnership(opDoorLine1));
    OwnerPtr<ICurve3d> opDoorLine2 = ILine3d::Create(Vector3d(-800, 0, 0), Vector3d(-800, 800, 0));
    opCrvs.emplace_back(TransferOwnership(opDoorLine2));
    OwnerPtr<ICurve3d> opDoorArc1 = IArc3d::Create(Vector3d(800, 0, 0), Vector3d(800, 800, 0), Vector3d::Zero, true);
    opCrvs.emplace_back(TransferOwnership(opDoorArc1));
    OwnerPtr<ICurve3d> opDoorArc2 = IArc3d::Create(Vector3d(-800, 0, 0), Vector3d(-800, 800, 0), Vector3d::Zero, true);
    opCrvs.emplace_back(TransferOwnership(opDoorArc2));
    OwnerPtr<IGraphicsCurve3dList> opGCrvs = IGraphicsCurve3dList::Create(TransferOwnership(opCrvs));
    opGCrvs->SetId(GraphicsNodeId(101));

    OwnerPtr<IGraphicsStyleData> opStyleData = IGraphicsStyleData::Create();
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(opStyleData, L"opStyleData为空", L"GDMPLab", L"2024-03-30");
    opStyleData->SetProjectionLineColor(Color::Red);
    IGraphicsStyle* pStyleElem = IGraphicsStyle::Create(pDocument, ElementCreationOptions::Normal);
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pStyleElem, L"pStyleElem为空", L"GDMPLab", L"2024-03-30");
    pStyleElem->SetGraphicsStyleData(*opStyleData);
    opGCrvs->SetGraphicsStyleId(pStyleElem->GetElementId());

    opGRep->AddChild(TransferOwnership(opGCrvs));

    OwnerPtr<IMesh> opMesh = AlgorithmMeshOperate::CreateDelaunayMeshFromPoints({ {0,0,0}, {1000, 0,0},{0,1000,0},{1000,1000,0} }, {});
    OwnerPtr<IGraphicsMeshBody> opGMesh = IGraphicsMeshBody::Create(TransferOwnership(opMesh));
    opGMesh->SetId(GraphicsNodeId(105));
    opGMesh->SetEdgesVisibilityMode(MeshEdgesVisibilityMode::AllEdgesInvisible);
    opGMesh->SetIsBackFaceVisible(false);
    opGRep->AddChild(TransferOwnership(opGMesh));

    IElementModelShape* pModelShape = pGenericElement->GetElementModelShape();
    pModelShape->SetGraphicsElementShape(TransferOwnership(opGRep));

    return pViewSpecificElem;
}

DBOBJECT_DATA_DEFINE(ViewSpecificShapeCustomComponent)
{
}

Sample::ViewSpecificShapeCustomComponent::~ViewSpecificShapeCustomComponent()
{
}

bool ViewSpecificShapeCustomComponent::SetOwnerElement(IElement* pOwnerElement)
{
    m_pOwnerElement = pOwnerElement;
    if (GetGraphicsNodeVisibilityCustomizer())
    {
        GetGraphicsNodeVisibilityCustomizer()->SetOwnerElement(pOwnerElement);
    }
    return true;
}

gcmp::NdbObject * Sample::ViewSpecificShapeCustomComponent::GetTopOwnerObject() const
{
    return quick_cast<gcmp::NdbObject>(m_pOwnerElement);
}

// 对于平面视图，如果开启了剖切面，只有图元在被剖切时视图专有GRep可见
bool ViewSpecificShapeCustomComponent::IsVisibleInModelView(const gcmp::IModelView* pModelView) const
{
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pModelView, L"pModelView为空", L"GDMPLab", L"2024-03-30");
    // 判断是二维视图
    if (pModelView->GetViewType() == BuiltInViewType::TwoDimensional)
    {
        // 判断是平面视图
        if (Vector3dUtils::IsParallel(pModelView->GetViewDirection(), Vector3d::UnitZ))
        {
            double cutOffset = 0;
            // 如果开启了剖切面
            if (pModelView->GetCutPlaneOffset(cutOffset))
            {
                // 剖切面的绝对高度
                double cutPlaneZValue = pModelView->GetOrigin().Z() + cutOffset;

                const IElement* pConstOwnerElement = m_pOwnerElement;
                DBG_WARN_AND_RETURN_FALSE_UNLESS(pConstOwnerElement, L"pConstOwnerElement为空", L"GDMPLab", L"2024-03-30");
                const IElementPosition* pPosition = pConstOwnerElement->GetElementPosition();
                DBG_WARN_AND_RETURN_FALSE_UNLESS(pPosition, L"pPosition为空", L"GDMPLab", L"2024-03-30");
                Matrix4d mat = pPosition->ComputeLocalToWorldCoordinateSystemTransformMatrix();
                // 门示例图元的定位点
                Vector3d transformedDoorOrigin = Vector3dUtils::TransformPoint(Vector3d::Zero, mat);
                // 门高2000，门的平面视图专业GRep的可见条件是：剖切面在门的底部和顶部之间，即门被剖切到。
                return (cutPlaneZValue - transformedDoorOrigin.Z() <= 2000) && (cutPlaneZValue - transformedDoorOrigin.Z() >= 0);
            }
        }
    }

    return true;
}

gcmp::OwnerPtr<IGraphicsElementShape> ViewSpecificShapeCustomComponent::CreateViewSpecificElementShape(const IModelView* pModelView) const
{
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pModelView, L"pModelView为空", L"GDMPLab", L"2024-03-30");

    // 这里必须用const接口，否则会触发事务
    const IElement* pConstOwnerElement = m_pOwnerElement;
    const IElementPosition* pPosition = pConstOwnerElement->GetElementPosition();
    Matrix4d mat = pPosition->ComputeLocalToWorldCoordinateSystemTransformMatrix();

    // 只针对二维视图
    if (pModelView->GetViewType() == BuiltInViewType::TwoDimensional)
    {
        // 平面视图，创建双开门符号
        if (Vector3dUtils::IsParallel(pModelView->GetViewDirection(), Vector3d::UnitZ))
        {
            gcmp::OwnerPtr<IGraphicsElementShape> opGRep = IGraphicsElementShape::Create(GraphicsRenderLayer::Model);
            OwnerPtr<IGraphicsCurve3d> opDoorLine1 = IGraphicsCurve3d::Create(ILine3d::Create(Vector3d(800, 0, 0), Vector3d(800, 800, 0)).get());
            opGRep->AddChild(TransferOwnership(opDoorLine1));

            OwnerPtr<IGraphicsCurve3d> opDoorLine2 = IGraphicsCurve3d::Create(ILine3d::Create(Vector3d(-800, 0, 0), Vector3d(-800, 800, 0)).get());
            opGRep->AddChild(TransferOwnership(opDoorLine2));

            OwnerPtr<IGraphicsCurve3d> opDoorArc1 = IGraphicsCurve3d::Create(IArc3d::Create(Vector3d(800, 0, 0), Vector3d(800, 800, 0), Vector3d::Zero, true).get());
            opGRep->AddChild(TransferOwnership(opDoorArc1));

            OwnerPtr<IGraphicsCurve3d> opDoorArc2 = IGraphicsCurve3d::Create(IArc3d::Create(Vector3d(-800, 0, 0), Vector3d(-800, 800, 0), Vector3d::Zero, true).get());
            opGRep->AddChild(TransferOwnership(opDoorArc2));

            opGRep->Transform(mat);

            return TransferOwnership(opGRep);
        }

        Vector3d transformedDoorDir = Vector3dUtils::TransformVector(Vector3d::UnitY, mat);
        // 如果视图正对门的正立面
        if (Vector3dUtils::IsParallel(transformedDoorDir, pModelView->GetViewDirection()))
        {
            const IElementModelShape* pElemModelShape = pConstOwnerElement->GetElementModelShape();
            const IGraphicsElementShape* pGRep = pElemModelShape->GetGraphicsElementShape();
            // 首先复制一份模型GRep
            gcmp::OwnerPtr<IGraphicsElementShape> opGRep = TransferOwnershipQuickCast<IGraphicsElementShape>(pGRep->Clone()); // IGraphicsElementShape::Create(GraphicsRenderLayer::Model);

            // 创建开启符号
            OwnerPtr<IGraphicsNodeGroup> opGGroup = IGraphicsNodeGroup::Create();
            OwnerPtr<IGraphicsCurve3d> opDoorLine6 = IGraphicsCurve3d::Create(ILine3d::Create(Vector3d(0, 0, 0), Vector3d(-800, 0, 1000)).get());
            opGGroup->AddChild(TransferOwnership(opDoorLine6));
            OwnerPtr<IGraphicsCurve3d> opDoorLine7 = IGraphicsCurve3d::Create(ILine3d::Create(Vector3d(0, 0, 0), Vector3d(800, 0, 1000)).get());
            opGGroup->AddChild(TransferOwnership(opDoorLine7));
            OwnerPtr<IGraphicsCurve3d> opDoorLine8 = IGraphicsCurve3d::Create(ILine3d::Create(Vector3d(0, 0, 2000), Vector3d(800, 0, 1000)).get());
            opGGroup->AddChild(TransferOwnership(opDoorLine8));
            OwnerPtr<IGraphicsCurve3d> opDoorLine9 = IGraphicsCurve3d::Create(ILine3d::Create(Vector3d(0, 0, 2000), Vector3d(-800, 0, 1000)).get());
            opGGroup->AddChild(TransferOwnership(opDoorLine9));

            opGGroup->Transform(mat);

            // 复制的模型GRep + 开启符号
            opGRep->AddChild(TransferOwnership(opGGroup));

            return TransferOwnership(opGRep);
        }
    }
    return nullptr;
}

void ViewSpecificShapeCustomComponent::SetRenderPriorityOffsetProvider(OwnerPtr<IRenderPriorityOffsetProvider> opProvider)
{
    SetRenderPriorityOffsetProvider__(TransferOwnership(opProvider));
}

const IRenderPriorityOffsetProvider* ViewSpecificShapeCustomComponent::GetRenderPriorityOffsetProvider() const
{
    return GetRenderPriorityOffsetProvider__().get();
}

IRenderPriorityOffsetProvider* ViewSpecificShapeCustomComponent::GetRenderPriorityOffsetProvider()
{
    return GetRenderPriorityOffsetProviderFW__().get();
}

void Sample::ViewSpecificShapeCustomComponent::SetGraphicsNodeVisibilityCustomizer(gcmp::OwnerPtr<gcmp::IGraphicsNodeVisibilityCustomizer> opCustomizer)
{
    SetSampleGraphicsNodeVisibilityCustomizer__(TransferOwnership(opCustomizer));
}

gcmp::IGraphicsNodeVisibilityCustomizer * Sample::ViewSpecificShapeCustomComponent::GetGraphicsNodeVisibilityCustomizer()
{
    return GetSampleGraphicsNodeVisibilityCustomizerFW__().get();
}

const gcmp::IGraphicsNodeVisibilityCustomizer* Sample::ViewSpecificShapeCustomComponent::GetGraphicsNodeVisibilityCustomizer() const
{
    return GetSampleGraphicsNodeVisibilityCustomizer__().get();
}


