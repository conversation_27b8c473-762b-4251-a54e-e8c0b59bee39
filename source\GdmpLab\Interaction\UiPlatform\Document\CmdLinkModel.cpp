﻿#include "CmdLinkModel.h"
#include "GbmpUiPlatformCommandIds.h"
#include "GbmpUiPlatformCommandRegister.h"
#include "GcmpCommandState.h"

#include "IUiDocumentViewManager.h"
#include "IUiView.h"
#include "IUiDocument.h"
#include "IDocumentManager.h"
#include "IDocument.h"
#include "IDocumentManager.h"
#include "IUserTransaction.h"
#include "IFileExtensions.h"
#include "ILinkDocumentType.h"
#include "ILinkDocumentElement.h"
#include "IGraphicsNodeReference.h"
#include "IElementModelShape.h"
#include "IGraphicsElementShape.h"
#include "IElementBasicInformation.h"
#include "ILinkElementProxy.h"
#include "ISelection.h"
#include "IElementFilter.h"
#include "FileUtility.h"
#include "ElementUtils.h"
#include "DebugMode.h"
#include "GcmpModel.h"
#include "MoveUtils.h"
#include "CommandParameters.h"
#include "GbmpWindows.h"
#include "GbmpFileSystem.h"
#include "NotificationUtils.h"
#include "GcmpBuiltInCategoryUniIdentities.h"
#include "GbmpLinkDocumentOpenValidator.h" 
#include "LinkManagerdlg.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;

CmdLinkModel::CmdLinkModel()
    : CommandBase(ID_CMD_LINK_MODEL, true)
{
}

CmdLinkModel::CmdLinkModel(const std::wstring& commandId, bool hasCommandData)
    : CommandBase(commandId, hasCommandData)
{
}

gcmp::OwnerPtr<IAction> CmdLinkModel::ExecuteCommand(const gcmp::CommandParameters& cmdParams)
{
    //开始链接文档
    IUiView* pUIView = IUiDocumentViewManager::Get()->GetCurrentUiView();
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pUIView != nullptr, L"IUiView为空", L"GDMPLab", L"2024-08-01");

    IUiDocument* pUIDoc = pUIView->GetUiDocument();
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pUIDoc != nullptr, L"IUiDocument为空", L"GDMPLab", L"2024-08-01");

    IDocument* pDocument = pUIDoc->GetDbDocument();
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pDocument, L"IDocument为空", L"GDMPLab", L"2024-08-01");

    OwnerPtr<IUserTransaction> opUserTransaction = IUserTransaction::Create(pDocument, GBMP_TR(L"链接模型文件"));

    LinkLoadResult linkLoadResult;
    ILinkDocumentType * pLinkDocumentType = this->CreateLinkDocumentType(pDocument, &linkLoadResult);
    if (nullptr == pLinkDocumentType)
    {
        if (LinkLoadResult::SameModelAsHost == linkLoadResult)
        {
            UiCommonDialog::ShowMessageBox(L"链接警告", L"链接文件和当前文档相同", (int)UiCommonDialog::ButtonType::OK);
        }
        else if (LinkLoadResult::OpenAsHost == linkLoadResult)
        {
            UiCommonDialog::ShowMessageBox(L"链接警告", L"被链接的文件已在应用中以主文档方式打开", (int)UiCommonDialog::ButtonType::OK);
        }
        else if (LinkLoadResult::IsFamilyFile == linkLoadResult)
        {
            UiCommonDialog::ShowMessageBox(L"链接警告", L"链接文件为族文件，目前暂不支持链接族文件", (int)UiCommonDialog::ButtonType::OK);
        }
        else if (LinkLoadResult::AlreadyLinked == linkLoadResult)
        {
            UiCommonDialog::ShowMessageBox(L"链接警告", L"被链接的文件已被当前文档链接", (int)UiCommonDialog::ButtonType::OK);
        } 

        return nullptr;
    }

    OwnerPtr<GbmpLinkDocumentOpenValidator> opLinkDocumentOpenValidator = NEW_AS_OWNER_PTR(GbmpLinkDocumentOpenValidator);
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(opLinkDocumentOpenValidator, L"opLinkDocumentOpenValidator创建失败", L"GDMPLab", L"2024-08-01");
    std::set<std::wstring> unrecognizedAssemblies;
    DocumentLinkLoadInfo docLoadInfo;
    docLoadInfo.pDocOpenValidator = opLinkDocumentOpenValidator.get();
    docLoadInfo.pUnrecognizedAssemblies = &unrecognizedAssemblies; 
    linkLoadResult = pLinkDocumentType->Load(docLoadInfo); 

    if (!unrecognizedAssemblies.empty())
    {
        std::wstring unrecognizedAssemblyInfo = L"";
        FOR_EACH(assemblyInfo, unrecognizedAssemblies)
        {
            unrecognizedAssemblyInfo += assemblyInfo + L",";
        }
        UiCommonDialog::ShowMessageBox(L"链接警告", L"链接文件存在插件未加载:" + unrecognizedAssemblyInfo + L"插件未加载", (int)UiCommonDialog::ButtonType::OK);
    }

    if (LinkLoadResult::SameModelAsHost == linkLoadResult)
    {
        UiCommonDialog::ShowMessageBox(L"链接警告", L"链接文件和当前文档相同", (int)UiCommonDialog::ButtonType::OK);
        return nullptr;
    }
    else if (LinkLoadResult::OpenAsHost == linkLoadResult)
    {
        UiCommonDialog::ShowMessageBox(L"链接警告", L"被链接的文件已在应用中以主文档方式打开", (int)UiCommonDialog::ButtonType::OK);
        return nullptr;
    }
    else if (LinkLoadResult::IsFamilyFile == linkLoadResult)
    {
        UiCommonDialog::ShowMessageBox(L"链接警告", L"链接文件为族文件，目前暂不支持链接族文件", (int)UiCommonDialog::ButtonType::OK);
        return nullptr;
    }
    else if (LinkLoadResult::AlreadyLinked == linkLoadResult)
    {
        UiCommonDialog::ShowMessageBox(L"链接警告", L"被链接的文件已被当前文档链接", (int)UiCommonDialog::ButtonType::OK);
        return nullptr;
    }
    else if (LinkLoadResult::NotOpenable == linkLoadResult)
    {
        if (opLinkDocumentOpenValidator && opLinkDocumentOpenValidator->GetErrorMessage() != L"")
        {
            UiCommonDialog::ShowMessageBox(L"链接警告", L"链接文件打开失败:" + opLinkDocumentOpenValidator->GetErrorMessage(), (int)UiCommonDialog::ButtonType::OK);
            return nullptr;
        }
        else
        {
            UiCommonDialog::ShowMessageBox(L"链接警告", L"链接文件打开失败", (int)UiCommonDialog::ButtonType::OK);
            return nullptr;
        }
    }
    else if (LinkLoadResult::Loaded != linkLoadResult)
    {
        UiCommonDialog::ShowMessageBox(GBMP_TR(L"错误"),
            L"链接模型" + pLinkDocumentType->GetLinkDatasetId() + L"失败", //+ loadFailMessage,
            (int)UiCommonDialog::ButtonType::OK);
        return nullptr;
    }

    // 首次加载放置对象

    ElementId linkDocumentTypeId = pLinkDocumentType->GetElementId();

    ILinkDocumentElement * pLinkDocumentElement = ILinkDocumentElement::Create(pDocument, linkDocumentTypeId);

    if (nullptr == pLinkDocumentElement)
    {
        UiCommonDialog::ShowMessageBox(GBMP_TR(L"错误"), L"放置链接对象失败",(int)UiCommonDialog::ButtonType::OK);
        return nullptr;
    } 

    UiCommonDialog::ButtonType btnType = this->GetPlacementOption();

    if (btnType == UiCommonDialog::ButtonType::No)
    {
        Box3d linkDocElemBox = pLinkDocumentElement->GetBoundingBox();
        Box3d boundingBox(Vector3d(-100, -100, -100), Vector3d(100, 100, 100));
        std::vector<IElement*> pHostDocElements = pDocument->GetAllElements();
        const IElementFilter* pElemFilter = IDocumentManager::Get()->GetLinkDocumentElementFilter();
        FOR_EACH(pHostDocElement, pHostDocElements)
        {
            if (!pElemFilter->PassesFilter(pHostDocElement))
                continue;

            if (ElementUtils::IsProxyElement(pHostDocElement))
                continue;

            if (IsA<ILinkDocumentElement>(pHostDocElement))
                continue;

            if (const IElementModelShape* pHostDocElementModelShape = pHostDocElement->GetElementModelShape())
            {
                if (const IGraphicsElementShape* pHostElemGraphicsElementShape = pHostDocElementModelShape->GetGraphicsElementShape())
                {
                    Box3d box3d = pHostElemGraphicsElementShape->GetBox();
                    boundingBox.MergeBox(box3d);
                }
            }
        }

        Vector3d moveDir = boundingBox.GetCenter().Substract(linkDocElemBox.GetCenter());
        MoveUtils::MoveElement(pLinkDocumentElement, moveDir);
    }

    DBG_WARN_AND_RETURN_NULLPTR_UNLESS( opUserTransaction->CommitWithoutRedoCapability(),L"提交链接模型文件事务失败", L"GDMPLab", L"2024-08-01");

    //if (DEBUG_MODE(LocalLinkGdcModelView))
    //{
        // LoadDefault3DData(pLinkDocumentElement);
    //}

    NotificationUtils::PostNotification( NotificationTypes::SuccessNotificationType, L"链接成功！");
   
    return nullptr;
}
 
UiCommonDialog::ButtonType CmdLinkModel::GetPlacementOption()
{
    return UiCommonDialog::ShowMessageBox(GBMP_TR(L"放置"), L"是否按零点到零点的方式放置？",(int)UiCommonDialog::ButtonType::Yes | (int)UiCommonDialog::ButtonType::No);
}

ILinkDocumentType* CmdLinkModel::CreateLinkDocumentType(IDocument* parentDoc, LinkLoadResult* pLinkLoadResult)
{
    std::wstring filePathName = UiCommonDialog::ShowOpenFileDialog(GBMP_TR(L"请选择模型文件"),FileSystem::GetWorkingDirPath(), StringUtil::FormatWString(GBMP_TR(L"GLAB文件(*%s)"), gcmp::IDocumentManager::Get()->GetFileExtensions()->GetDefaultFileExt()));

    if (filePathName.empty())
    { 
        if (pLinkLoadResult) {
            *pLinkLoadResult = LinkLoadResult::Unknown;
        }
        return nullptr;
    }

    LinkDocumentTypeOptions options(-1, L"", false, false); // 保留对老的API的覆盖
    ILinkDocumentType * pLinkDocumentType = ILinkDocumentType::Create(parentDoc, filePathName, options, pLinkLoadResult);
    return pLinkDocumentType;
}

bool CmdLinkModel::IsVisible() const
{
    return GcmpCommandState::IsInDocumentEnvironment();
}

bool CmdLinkModel::IsEnabled() const
{
    return GcmpCommandState::IsInDocumentEnvironment();
}

REGISTER_GM_COMMAND(CmdLinkModel)
 
gcmp::CmdLinkManagerModel::CmdLinkManagerModel()
    : CommandBase(ID_CMD_LINK_MANAGER, true)
{

}

gcmp::OwnerPtr<gcmp::IAction> gcmp::CmdLinkManagerModel::ExecuteCommand(const gcmp::CommandParameters& cmdParams)
{
    IUiDocumentViewManager* pUiDocViewManager = IUiDocumentViewManager::Get();
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pUiDocViewManager, L"无效的pUiDocViewManager指针", L"GDMPLab", L"2024-08-01");
    IUiDocument* pUIDoc = pUiDocViewManager->GetCurrentUiDocument(); 
    DBG_WARN_AND_RETURN_NULLPTR_UNLESS(pUIDoc != nullptr, L"无效的pUIDoc指针", L"GDMPLab", L"2024-08-01"); 
    LinkManagerDlg dlg(pUIDoc);
    dlg.exec();
    return nullptr;
}

REGISTER_GM_COMMAND(CmdLinkManagerModel)
