#include "SampleParameterEnums.h"
#include "IParameterDefinitionLibrary.h"
#include "IParameterDefinition.h"
#include "IParameterValueList.h"
#include "SampleBuiltInParameterDefinitions.h"
#include "IParameterDisplayCreator.h"
#include "OneTimeInitializer.h"
#include "IParameterValueDisplayString.h"
#include "IParameterValueInt.h"
#include "SampleParameterList.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;
using namespace Sample;

namespace
{
    void AddSampleParameterDisplayList()
    {
        {
            ParameterValueDisplayStrings colorModeList;
            colorModeList.emplace_back(IParameterValueDisplayString::CreateParameterValueDisplayString(IParameterValueInt::Create(int(ColorMode::ByColor)), GBMP_TR(L"��ɫ")));
            colorModeList.emplace_back(IParameterValueDisplayString::CreateParameterValueDisplayString(IParameterValueInt::Create(int(ColorMode::ByColorIndex)), GBMP_TR(L"����ɫ")));
            colorModeList.emplace_back(IParameterValueDisplayString::CreateParameterValueDisplayString(IParameterValueInt::Create(int(ColorMode::SmartUseBackgroundColor)), GBMP_TR(L"���ܱ���ɫ")));
            colorModeList.emplace_back(IParameterValueDisplayString::CreateParameterValueDisplayString(IParameterValueInt::Create(int(ColorMode::SmartAntiBackgroundColor)), GBMP_TR(L"���ܱ�����ɫ")));
            IParameterDisplayCreator::AddStacticDropdownListParameterDisplay(PARAMETER_UID(ParameterSample_ColorModeList), colorModeList);
        }
        {
            OwnerPtr<IParameterValueList> opMaterialList = IParameterValueList::CreateParameterValueMaterialList(PARAMETER_UID(ParameterSample_Material));
            IParameterDisplayCreator::AddDynamicDropdownListParameterDisplay(TransferOwnership(opMaterialList));
        }
        {
            OwnerPtr<IParameterValueList> opViewlList = NEW_AS_OWNER_PTR(SampleParameterViewList, PARAMETER_UID(ParameterSample_ViewList));
            IParameterDisplayCreator::AddDynamicDropdownListParameterDisplay(TransferOwnership(opViewlList));
        }
    }

    ONE_TIME_INITIALIZER(AddSampleParameterDisplayList);
}