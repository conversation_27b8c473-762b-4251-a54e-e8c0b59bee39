#pragma once

#include "ElemModule.h"
#include "UniIdentity.h"
#include "IParameterValueList.h"
#include "IParameterValueStorage.h"
#include "NdbObjectSchematic.h"
#include "DbDataSchema.h"

using namespace gcmp;

namespace gcmp
{
    class IElement;
    class IDocument;
}

namespace Sample
{
    class SampleParameterViewList : public gcmp::NdbObjectSchematic, public gcmp::IParameterValueList
    {
        DBOBJECT_DATA_BEGIN_AND_QUICK_CAST_FROM(Sample, SampleParameterViewList, gcmp::NdbObjectSchematic, 3293BB93 - 27CE - 42B1 - 9370 - C05FB6419AFA, 
            ElemModule, gcmp::IParameterValueList)
            DATA(UniIdentity, Uid)
            DATA_TRANSIENT(IElement*, pOwnerElement)
            DBOBJECT_DATA_END

    public:
        SampleParameterViewList(const gcmp::UniIdentity& uid) : m_Uid(uid), m_pOwnerElement(nullptr) {};
        ~SampleParameterViewList(){}

        // 通过 NdbObjectSchematic 继承
        NdbObject* GetTopOwnerObject() const override;

        OwnerPtr<IParameterValueList> Clone() const override;

        const UniIdentity& GetUid() const override { return GetUid__(); }

        std::wstring GetDisplayStringByValue(const IDocument* pDocument, const IParameterValueStorage* pValue, const std::vector<ElementId>& elementIds = std::vector<ElementId>()) const override;

        OwnerPtr<IParameterValueStorage> GetValueByDisplayString(const IDocument* pDocument, const std::wstring& displayString, const std::vector<ElementId>& elementIds = std::vector<ElementId>()) const override;

        std::vector<std::wstring> GetDisplayStrings(const IDocument* pDocument, const std::vector<ElementId>& elementIds) const override;

        std::vector<OwnerPtr<IParameterValueStorage>> GetValues(const IDocument* pDocument, const std::vector<ElementId>& ids) const override;

        bool HasOuterEditor() const override;

        bool IsManualInputModifiable(const IDocument* pDoc) const override;
    private:
        static const std::wstring s_nullName;
        static const std::wstring s_searchName;
    };
}
