#include "SampleParameterList.h"
#include "DbObjectUtils.h"
#include "IModelView.h"
#include "IDocument.h"
#include "GcmpBuiltInCategoryUniIdentities.h"
#include "IParameterValueElementId.h"
#include "IParameterValueString.h"
#include "UiCommonDialog.h"
#include "IElementParameters.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;
using namespace Sample;

const std::wstring SampleParameterViewList::s_nullName = GBMP_TR(L"< 空 >");
const std::wstring SampleParameterViewList::s_searchName = GBMP_TR(L"< 搜索 >");

DBOBJECT_DATA_DEFINE(SampleParameterViewList)
{
    m_pOwnerElement = nullptr;
}

NdbObject* Sample::SampleParameterViewList::GetTopOwnerObject() const
{
    return nullptr;
}

OwnerPtr<IParameterValueList> Sample::SampleParameterViewList::Clone() const
{
    return OwnerPtr<IParameterValueList>();
}

std::wstring Sample::SampleParameterViewList::GetDisplayStringByValue(const IDocument* pDocument, const IParameterValueStorage* pValue, const std::vector<ElementId>& elementIds) const
{
    std::wstring res;
    DBG_WARN_AND_RETURN_UNLESS(pDocument, res, L"pDocument为空", L"GDMPLab", L"2024-03-30");
    DBG_WARN_AND_RETURN_UNLESS(pValue, res, L"pValue为空", L"GDMPLab", L"2024-03-30");

    const IParameterValueElementId* pParamElementId = dynamic_cast<const IParameterValueElementId*>(pValue);
    if (pParamElementId)
    {
        if (!pParamElementId->GetValue().IsValid())
        {
            return s_nullName;
        }

        const IModelView* pModelView = quick_cast<IModelView>(pDocument->GetElement(pParamElementId->GetValue()));
        if (pModelView)
        {
            return pModelView->GetName();
        }
    }
    const IParameterValueString* pParamString = dynamic_cast<const IParameterValueString*>(pValue);
    if (pParamString)
    {
        return pParamString->GetValue();
    }
    return s_nullName;
}

OwnerPtr<IParameterValueStorage> Sample::SampleParameterViewList::GetValueByDisplayString(const IDocument* pDocument, const std::wstring& displayString, const std::vector<ElementId>& elementIds) const
{
    std::wstring searchString = displayString;
    bool isSearch = false;
    if (displayString == s_searchName)
    {
        std::wstring inputTxt;
        if (UiCommonDialog::ShowInputBox(L"搜索视图", L"输入搜索字符", L"", inputTxt) == UiCommonDialog::ButtonType::OK)
        {
            searchString = inputTxt;
        }
        isSearch = true;
    }
    else if (displayString == s_nullName)
    {
        return IParameterValueElementId::Create(ElementId::InvalidID);
    }

    std::vector<IElement*> pViews = pDocument->GetElementsByCategory(BuiltInCategoryUniIdentities::BICU_MODEL_VIEW);
    for (auto pElem : pViews)
    {
        const IModelView* pView = quick_cast<IModelView>(pElem);
        if(pView->GetName().find(searchString) != std::wstring::npos)
        {
            return OwnerPtr<IParameterValueStorage>(IParameterValueElementId::Create(pView->GetElementId()));
        }
    }
    if (isSearch)
    {
        return OwnerPtr<IParameterValueStorage>(IParameterValueString::Create(s_nullName));
    }
    else
    {
        return OwnerPtr<IParameterValueStorage>(IParameterValueString::Create(displayString));
    }
}

std::vector<std::wstring> Sample::SampleParameterViewList::GetDisplayStrings(const IDocument* pDocument, const std::vector<ElementId>& elementIds) const
{
    std::vector<std::wstring> res;
    DBG_WARN_AND_RETURN_UNLESS(pDocument, res, L"无效的参数pDoc", L"GDMPLab", L"2024-03-30");

    std::vector<OwnerPtr<IParameterValueStorage>> opValues = GetValues(pDocument, elementIds);
    for(OwnerPtr<IParameterValueStorage>& opValue : opValues)
    {
        res.emplace_back(GetDisplayStringByValue(pDocument, opValue.get()));
    }

    return res;
}

std::vector<OwnerPtr<IParameterValueStorage>> Sample::SampleParameterViewList::GetValues(const IDocument* pDocument, const std::vector<ElementId>& ids) const
{
    std::vector<OwnerPtr<IParameterValueStorage>> res;
    res.emplace_back(IParameterValueString::Create(s_searchName));
    res.emplace_back(IParameterValueString::Create(s_nullName));

    std::vector<ElementId> pViewIds;
    std::vector<IElement*> pViews = pDocument->GetElementsByCategory(BuiltInCategoryUniIdentities::BICU_MODEL_VIEW);
    for(auto pView : pViews)
    {
        pViewIds.emplace_back(pView->GetElementId());
        res.emplace_back(IParameterValueElementId::Create(pView->GetElementId()));
    }

    // 获取第一个Element的参数值。
    if (ids.size() > 0)
    {
        const IElement* pElement = pDocument->GetElement(ids[0]);
        DBG_WARN_AND_RETURN_UNLESS(pElement, res, L"pElement为空", L"GDMPLab", L"2024-03-30");

        const IElementParameters* pElementParameters = pElement->GetElementParameters();
        if (pElementParameters)
        {
            pElementParameters->FilterParameterValues(GetUid(), res);
        }
    }

    return res;
}

bool Sample::SampleParameterViewList::HasOuterEditor() const
{
    return true;
}

bool Sample::SampleParameterViewList::IsManualInputModifiable(const IDocument* pDoc) const
{
    return true;
}

